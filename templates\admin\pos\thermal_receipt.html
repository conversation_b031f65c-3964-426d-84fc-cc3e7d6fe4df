<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال - {{ payment.payment_number }}</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 10mm;
            }

            body {
                margin: 0;
                padding: 0;
                font-family: 'Arial', 'Tahoma', sans-serif;
                font-size: 14px;
                line-height: 1.4;
                color: #000;
                background: #fff;
                width: 100%;
            }

            .receipt {
                width: 100%;
                max-width: 100%;
                padding: 0;
                box-sizing: border-box;
            }

            .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid #000;
            }

            .logo {
                margin-bottom: 10px;
            }

            .logo img {
                max-height: 80px;
                max-width: 200px;
                object-fit: contain;
            }

            .shop-name {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 8px;
                color: #000;
            }

            .shop-info {
                font-size: 12px;
                margin-bottom: 5px;
                color: #333;
            }

            .receipt-title {
                font-size: 20px;
                font-weight: bold;
                margin: 15px 0;
                text-align: center;
                background: #f0f0f0;
                padding: 10px;
                border: 1px solid #000;
            }

            .receipt-info {
                margin-bottom: 20px;
                font-size: 12px;
                background: #f9f9f9;
                padding: 10px;
                border: 1px solid #ddd;
            }

            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .info-item {
                margin-bottom: 5px;
            }

            .info-label {
                font-weight: bold;
                color: #333;
            }

            .items-section {
                margin-bottom: 20px;
            }

            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
            }

            .items-table th,
            .items-table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: center;
                font-size: 12px;
            }

            .items-table th {
                background: #000;
                color: #fff;
                font-weight: bold;
            }

            .item-name {
                text-align: right !important;
                font-weight: 500;
            }

            .totals {
                margin-bottom: 20px;
                font-size: 14px;
                border: 2px solid #000;
                padding: 15px;
            }

            .total-line {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                padding: 5px 0;
            }

            .grand-total {
                font-weight: bold;
                font-size: 18px;
                border-top: 2px solid #000;
                padding-top: 10px;
                margin-top: 10px;
                background: #f0f0f0;
                padding: 10px;
            }

            .payment-info {
                margin-bottom: 20px;
                font-size: 12px;
                background: #f9f9f9;
                padding: 15px;
                border: 1px solid #ddd;
            }

            .footer {
                text-align: center;
                font-size: 11px;
                margin-top: 30px;
                padding-top: 15px;
                border-top: 1px dashed #000;
            }

            .footer-message {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #000;
            }

            .print-info {
                margin-top: 15px;
                font-size: 10px;
                color: #666;
            }

            .no-print {
                display: none;
            }
        }

        @media screen {
            body {
                font-family: 'Arial', 'Tahoma', sans-serif;
                background: #f5f5f5;
                padding: 20px;
                direction: rtl;
            }

            .receipt {
                width: 210mm;
                margin: 0 auto;
                background: white;
                padding: 20mm;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 8px;
                min-height: 297mm;
            }

            .print-controls {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 10px;
            }

            .print-button {
                background: #007bff;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: background 0.3s;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .print-button:hover {
                background: #0056b3;
            }

            .close-button {
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: background 0.3s;
            }

            .close-button:hover {
                background: #545b62;
            }
        }
    </style>
</head>
<body>
    <!-- Print Controls (Screen Only) -->
    <div class="print-controls no-print">
        <button class="print-button" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة الإيصال
        </button>
        <button class="close-button" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="receipt">
        <!-- Header with Logo -->
        <div class="header">
            {% if settings.logo_b64 %}
                <div class="logo">
                    <img src="{{ settings.logo_b64 }}" alt="{{ settings.shop_name }}">
                </div>
            {% endif %}

            <div class="shop-name">{{ settings.shop_name }}</div>

            <div class="shop-info">
                {% if settings.phone %}
                    <div>📞 {{ settings.phone }}</div>
                {% endif %}
                {% if settings.address %}
                    <div>📍 {{ settings.address }}</div>
                {% endif %}
                {% if settings.email %}
                    <div>📧 {{ settings.email }}</div>
                {% endif %}
                {% if settings.tax_number %}
                    <div>🏛️ الرقم الضريبي: {{ settings.tax_number }}</div>
                {% endif %}
            </div>
        </div>

        <!-- Receipt Title -->
        <div class="receipt-title">
            🧾 إيصال دفع / Payment Receipt
        </div>

        <!-- Receipt Info -->
        <div class="receipt-info">
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">رقم الإيصال:</span>
                    <span>{{ payment.payment_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الطلب:</span>
                    <span>{{ payment.order.order_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span>
                    <span>{{ payment.payment_timestamp.strftime('%Y-%m-%d') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span>
                    <span>{{ payment.payment_timestamp.strftime('%H:%M:%S') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الكاشير:</span>
                    <span>{{ payment.processed_by.username }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">العميل:</span>
                    <span>{{ payment.order.customer_name or 'عميل عادي' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الطاولة:</span>
                    <span>{{ payment.order.table.table_number if payment.order.table else 'تيك أواي' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع الطلب:</span>
                    <span>{{ 'تناول في المطعم' if payment.order.table else 'تيك أواي' }}</span>
                </div>
            </div>
        </div>

        <!-- Items Section -->
        <div class="items-section">
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 8%;">#</th>
                        <th style="width: 45%;">اسم الصنف</th>
                        <th style="width: 12%;">الكمية</th>
                        <th style="width: 15%;">سعر الوحدة</th>
                        <th style="width: 20%;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in payment.order.order_items %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td class="item-name">{{ item.product.get_name('ar') }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ "%.2f"|format(item.unit_price) }} {{ payment.order.currency }}</td>
                            <td><strong>{{ "%.2f"|format(item.total_price) }} {{ payment.order.currency }}</strong></td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Totals -->
        <div class="totals">
            <div class="total-line">
                <span>💰 المجموع الفرعي:</span>
                <span><strong>{{ "%.2f"|format(payment.order.total_amount) }} {{ payment.order.currency }}</strong></span>
            </div>

            {% if payment.order.discount_amount and payment.order.discount_amount > 0 %}
                <div class="total-line">
                    <span>🎯 الخصم:</span>
                    <span style="color: #d63384;">-{{ "%.2f"|format(payment.order.discount_amount) }} {{ payment.order.currency }}</span>
                </div>
            {% endif %}

            {% if payment.order.tax_amount and payment.order.tax_amount > 0 %}
                <div class="total-line">
                    <span>🏛️ الضريبة:</span>
                    <span>{{ "%.2f"|format(payment.order.tax_amount) }} {{ payment.order.currency }}</span>
                </div>
            {% endif %}

            {% if payment.order.service_charge and payment.order.service_charge > 0 %}
                <div class="total-line">
                    <span>🛎️ رسوم الخدمة:</span>
                    <span>{{ "%.2f"|format(payment.order.service_charge) }} {{ payment.order.currency }}</span>
                </div>
            {% endif %}

            <div class="total-line grand-total">
                <span>💳 المجموع الكلي:</span>
                <span><strong>{{ "%.2f"|format(payment.order.total_amount) }} {{ payment.order.currency }}</strong></span>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="payment-info">
            <h4 style="margin: 0 0 10px 0; color: #000;">💳 تفاصيل الدفع</h4>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">طريقة الدفع:</span>
                    <span>{{ payment.get_payment_method_text('ar') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المبلغ المدفوع:</span>
                    <span><strong>{{ payment.get_formatted_amount() }}</strong></span>
                </div>
                {% if payment.reference_number %}
                    <div class="info-item">
                        <span class="info-label">رقم المرجع:</span>
                        <span>{{ payment.reference_number }}</span>
                    </div>
                {% endif %}
                <div class="info-item">
                    <span class="info-label">حالة الدفع:</span>
                    <span style="color: #198754; font-weight: bold;">{{ payment.get_status_text('ar') }}</span>
                </div>
                {% if payment.change_amount and payment.change_amount > 0 %}
                    <div class="info-item">
                        <span class="info-label">الباقي:</span>
                        <span style="color: #fd7e14; font-weight: bold;">{{ "%.2f"|format(payment.change_amount) }} {{ payment.order.currency }}</span>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-message">
                🌟 شكراً لزيارتكم - Thank You for Your Visit 🌟
            </div>

            <div style="margin: 15px 0;">
                <div>نتطلع لخدمتكم مرة أخرى</div>
                <div style="font-size: 12px; color: #666;">We look forward to serving you again</div>
            </div>

            {% if settings.facebook_url or settings.instagram_url or settings.whatsapp_url %}
                <div style="margin: 15px 0;">
                    <div style="font-weight: bold; margin-bottom: 5px;">تابعونا على:</div>
                    <div style="font-size: 10px;">
                        {% if settings.facebook_url %}📘 Facebook | {% endif %}
                        {% if settings.instagram_url %}📷 Instagram | {% endif %}
                        {% if settings.whatsapp_url %}📱 WhatsApp{% endif %}
                    </div>
                </div>
            {% endif %}

            <div style="margin: 20px 0; border-top: 2px solid #000; border-bottom: 2px solid #000; padding: 10px 0;">
                <div style="font-size: 12px; font-weight: bold;">
                    ⭐ قيّم تجربتك معنا ⭐
                </div>
                <div style="font-size: 10px; margin-top: 5px;">
                    Rate your experience with us
                </div>
            </div>

            <div class="print-info">
                <div>🖨️ تم الطباعة في: {{ moment().format('YYYY-MM-DD HH:mm:ss') }}</div>
                <div style="margin-top: 5px;">
                    🏪 {{ settings.shop_name }} - نظام إدارة المطاعم
                </div>
                <div style="margin-top: 5px; font-size: 9px;">
                    Restaurant Management System - Powered by Advanced POS
                </div>
            </div>

            <div style="margin-top: 20px; font-size: 16px;">
                ═══════════════════════════════════════
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };

        // Close window after printing
        window.onafterprint = function() {
            // Optionally close window after printing
            // window.close();
        };

        // Add print shortcut
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
